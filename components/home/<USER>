import { DiscoBallsSvg } from '@/components/svgs/DiscoBallsSvg';
import Image from 'next/image';
import { TitleSvg } from '../svgs';

type MainTitleProps = {
  className?: string;
};

export function MainTitle({
  className = '',
}: MainTitleProps) {
  return (
    <div className={className}>
      {/* Main Title Section with layered images */}
      <div className="relative my-12 flex items-center justify-center">

        {/* Disco Balls SVG - Background layer */}
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className="w-96 h-96 md:w-[480px] md:h-[480px] lg:w-[600px] lg:h-[600px]">
            <DiscoBallsSvg className="w-full h-full object-contain" />
          </div>
        </div>

        {/* Couple Photo - Middle layer */}
        <div className="relative w-64 h-80 md:w-80 md:h-96 lg:w-96 lg:h-[480px] z-20">
          <Image
            src="/sean_and_eva_banner_photo.png"
            alt="Sean and Eva"
            fill
            className="object-cover rounded-lg"
            priority
          />
        </div>

        {/* Title SVG - Top layer */}
        <div className="absolute inset-0 flex items-center justify-center z-30">
          <div className="w-64 h-48 md:w-80 md:h-64 lg:w-96 lg:h-80">
            <TitleSvg className="w-full h-full object-contain" />
          </div>
        </div>

      </div>
    </div>
  );
}
